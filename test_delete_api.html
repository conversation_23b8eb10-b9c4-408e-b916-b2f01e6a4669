<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>删除功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .delete-btn {
            background: #dc3545;
        }
        .delete-btn:hover {
            background: #c82333;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>校园卡删除功能测试</h1>
    
    <div class="test-section">
        <h2>1. 登录测试</h2>
        <button onclick="testLogin()">测试登录</button>
        <div id="login-result" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 报告校园卡测试</h2>
        <button onclick="testReportCard()">报告校园卡</button>
        <div id="report-result" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 查询校园卡测试</h2>
        <button onclick="testQueryCard()">查询校园卡</button>
        <div id="query-result" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 删除功能测试</h2>
        <button class="delete-btn" onclick="testDeleteCard()">测试删除功能</button>
        <div id="delete-result" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000';
        let currentUser = null;
        let testCardId = null;

        // API调用函数
        async function apiCall(endpoint, method = 'GET', data = null) {
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                },
            };

            if (data) {
                options.body = JSON.stringify(data);
            }

            try {
                const response = await fetch(`${API_BASE}${endpoint}`, options);
                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.error || 'API调用失败');
                }

                return result;
            } catch (error) {
                console.error('API调用错误:', error);
                throw error;
            }
        }

        // 显示结果
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.innerHTML = message;
            element.style.display = 'block';
        }

        // 测试登录
        async function testLogin() {
            try {
                const result = await apiCall('/login', 'POST', {
                    student_id: '2021001',
                    full_name: '张三',
                    password: '123456'
                });
                
                currentUser = result;
                showResult('login-result', `✅ 登录成功！用户：${result.full_name} (${result.student_id})`, 'success');
            } catch (error) {
                showResult('login-result', `❌ 登录失败：${error.message}`, 'error');
            }
        }

        // 测试报告校园卡
        async function testReportCard() {
            if (!currentUser) {
                showResult('report-result', '❌ 请先登录', 'error');
                return;
            }

            try {
                const formData = new FormData();
                formData.append('card_number', '2021001');
                formData.append('found_location', '图书馆');
                formData.append('handler_option', '1');
                formData.append('contact', '13800138000');
                formData.append('current_user_id', currentUser.user_id);
                formData.append('current_user_name', currentUser.full_name);

                const response = await fetch(`${API_BASE}/report_card`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                if (response.ok) {
                    showResult('report-result', '✅ 校园卡报告成功！', 'success');
                } else {
                    throw new Error(result.error || '报告失败');
                }
            } catch (error) {
                showResult('report-result', `❌ 报告失败：${error.message}`, 'error');
            }
        }

        // 测试查询校园卡
        async function testQueryCard() {
            try {
                const result = await apiCall(`/query_lost_card?student_id=2021001`);
                
                if (result.status === 'found') {
                    testCardId = result.card_id;
                    showResult('query-result', `✅ 查询成功！找到校园卡，ID: ${result.card_id}`, 'success');
                } else {
                    showResult('query-result', `ℹ️ 查询结果：${result.status}`, 'info');
                }
            } catch (error) {
                showResult('query-result', `❌ 查询失败：${error.message}`, 'error');
            }
        }

        // 测试删除功能
        async function testDeleteCard() {
            if (!currentUser) {
                showResult('delete-result', '❌ 请先登录', 'error');
                return;
            }

            if (!testCardId) {
                showResult('delete-result', '❌ 请先查询到校园卡记录', 'error');
                return;
            }

            if (!confirm('确定要测试删除功能吗？这将删除测试记录。')) {
                return;
            }

            try {
                const result = await apiCall('/delete_card_record', 'POST', {
                    card_id: testCardId,
                    current_user_student_id: currentUser.student_id
                });

                if (result.success) {
                    showResult('delete-result', '✅ 删除成功！记录已被标记为删除状态。', 'success');
                    testCardId = null; // 清空测试ID
                } else {
                    throw new Error(result.error || '删除失败');
                }
            } catch (error) {
                showResult('delete-result', `❌ 删除失败：${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
