#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试状态过滤功能的脚本
验证：
1. 公示列表只显示 status='found' 的记录
2. 热门地点统计包含所有状态的记录
"""

import requests
import json

API_BASE = 'http://localhost:5000'

def test_status_filtering():
    """测试状态过滤功能"""
    print("=== 状态过滤功能测试 ===\n")
    
    # 1. 测试用户登录
    print("1. 测试用户登录...")
    login_data = {
        'student_id': '2021001',
        'full_name': '张三',
        'password': '123456'
    }
    
    try:
        response = requests.post(f'{API_BASE}/login', json=login_data)
        if response.status_code == 200:
            user_info = response.json()
            print(f"✅ 用户登录成功: {user_info['full_name']}")
        else:
            print(f"❌ 用户登录失败: {response.text}")
            return
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return
    
    # 2. 创建测试数据 - 报告多张校园卡
    print("\n2. 创建测试数据...")
    test_cards = [
        {'card_number': '2021001', 'location': '图书馆', 'contact': '13800138001'},
        {'card_number': '2021002', 'location': '图书馆', 'contact': '13800138002'},
        {'card_number': '2021003', 'location': '食堂', 'contact': '13800138003'},
        {'card_number': '2021004', 'location': '食堂', 'contact': '13800138004'},
    ]
    
    created_cards = []
    for i, card_data in enumerate(test_cards):
        try:
            formData = {
                'card_number': card_data['card_number'],
                'found_location': card_data['location'],
                'handler_option': '1',
                'contact': card_data['contact'],
                'current_user_id': str(user_info['user_id']),
                'current_user_name': user_info['full_name']
            }
            
            response = requests.post(f'{API_BASE}/report_card', data=formData)
            if response.status_code == 200:
                print(f"✅ 创建测试卡片 {i+1}: {card_data['card_number']} ({card_data['location']})")
                created_cards.append(card_data['card_number'])
            else:
                result = response.json()
                if 'already exists' in result.get('error', '').lower():
                    print(f"ℹ️ 测试卡片 {i+1} 已存在: {card_data['card_number']}")
                    created_cards.append(card_data['card_number'])
                else:
                    print(f"❌ 创建测试卡片 {i+1} 失败: {result.get('error', 'Unknown error')}")
        except Exception as e:
            print(f"❌ 创建测试卡片 {i+1} 异常: {e}")
    
    # 3. 测试热门地点统计（应该包含所有状态的记录）
    print("\n3. 测试热门地点统计...")
    try:
        response = requests.get(f'{API_BASE}/hot_locations')
        if response.status_code == 200:
            hot_locations = response.json()
            print("✅ 热门地点统计获取成功:")
            
            if 'locations' in hot_locations:
                locations = hot_locations['locations']
                statistics = hot_locations.get('statistics', {})
                
                print(f"   总记录数: {statistics.get('total_cards', 0)}")
                print(f"   AI分析覆盖率: {statistics.get('ai_analysis_coverage', 0)}%")
                print("   热门地点:")
                for loc in locations[:5]:  # 显示前5个
                    print(f"     - {loc['location']}: {loc['count']}次 ({loc['percentage']}%)")
            else:
                # 兼容旧格式
                print("   热门地点:")
                for loc in hot_locations[:5]:
                    print(f"     - {loc['location']}: {loc['count']}次 ({loc['percentage']}%)")
        else:
            print(f"❌ 热门地点统计获取失败: {response.text}")
    except Exception as e:
        print(f"❌ 热门地点统计请求异常: {e}")
    
    # 4. 测试公示列表（应该只显示 status='found' 的记录）
    print("\n4. 测试公示列表...")
    try:
        response = requests.get(f'{API_BASE}/query_lost_card?student_id=dummy')
        if response.status_code == 200:
            result = response.json()
            if result.get('unmatched_cards'):
                unmatched_count = len(result['unmatched_cards'])
                print(f"✅ 公示列表获取成功，显示 {unmatched_count} 条记录")
                print("   公示列表中的记录:")
                for card in result['unmatched_cards'][:3]:  # 显示前3条
                    print(f"     - 卡号: {card['masked_info']['student_id']}, 地点: {card['found_location']}")
            else:
                print("ℹ️ 公示列表为空")
        else:
            print(f"❌ 公示列表获取失败: {response.text}")
    except Exception as e:
        print(f"❌ 公示列表请求异常: {e}")
    
    # 5. 删除一条记录，然后验证过滤效果
    print("\n5. 测试删除记录后的过滤效果...")
    if created_cards:
        # 查询第一张卡片
        test_card_number = created_cards[0]
        try:
            response = requests.get(f'{API_BASE}/query_lost_card?student_id={test_card_number}')
            if response.status_code == 200:
                query_result = response.json()
                if query_result['status'] == 'found':
                    card_id = query_result.get('card_id')
                    print(f"   找到测试卡片 {test_card_number}，ID: {card_id}")
                    
                    # 删除这张卡片
                    delete_data = {
                        'card_id': card_id,
                        'current_user_student_id': user_info['student_id']
                    }
                    
                    response = requests.post(f'{API_BASE}/delete_card_record', json=delete_data)
                    if response.status_code == 200:
                        delete_result = response.json()
                        if delete_result.get('success'):
                            print(f"✅ 成功删除卡片 {test_card_number}")
                            
                            # 6. 验证删除后的效果
                            print("\n6. 验证删除后的过滤效果...")
                            
                            # 6a. 验证公示列表不再显示已删除记录
                            response = requests.get(f'{API_BASE}/query_lost_card?student_id=dummy')
                            if response.status_code == 200:
                                result = response.json()
                                if result.get('unmatched_cards'):
                                    new_count = len(result['unmatched_cards'])
                                    print(f"   公示列表现在显示 {new_count} 条记录（应该比之前少1条）")
                                    
                                    # 检查是否还包含已删除的记录
                                    deleted_card_found = False
                                    for card in result['unmatched_cards']:
                                        if card['masked_info']['student_id'] == test_card_number:
                                            deleted_card_found = True
                                            break
                                    
                                    if not deleted_card_found:
                                        print("✅ 公示列表正确过滤：已删除记录不再显示")
                                    else:
                                        print("❌ 公示列表过滤失败：已删除记录仍然显示")
                            
                            # 6b. 验证热门地点统计仍包含已删除记录
                            response = requests.get(f'{API_BASE}/hot_locations')
                            if response.status_code == 200:
                                hot_locations = response.json()
                                if 'statistics' in hot_locations:
                                    total_cards = hot_locations['statistics'].get('total_cards', 0)
                                    print(f"   热门地点统计仍包含 {total_cards} 条记录（应该包含已删除的记录）")
                                    print("✅ 热门地点统计正确：包含所有状态的记录")
                        else:
                            print(f"❌ 删除失败: {delete_result.get('error')}")
                    else:
                        print(f"❌ 删除请求失败: {response.text}")
                else:
                    print(f"   测试卡片 {test_card_number} 未找到或状态不正确")
        except Exception as e:
            print(f"❌ 删除测试异常: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == '__main__':
    test_status_filtering()
