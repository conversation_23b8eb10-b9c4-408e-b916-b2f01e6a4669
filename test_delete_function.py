#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试删除功能的脚本
"""

import requests
import json

API_BASE = 'http://localhost:5000'

def test_delete_function():
    """测试删除功能"""
    print("=== 校园卡删除功能测试 ===\n")
    
    # 1. 测试用户注册
    print("1. 测试用户注册...")
    register_data = {
        'student_id': '2021001',
        'full_name': '测试用户',
        'password': '123456'
    }
    
    try:
        response = requests.post(f'{API_BASE}/register', json=register_data)
        if response.status_code == 201:
            print("✅ 用户注册成功")
        elif response.status_code == 409:
            print("ℹ️ 用户已存在，跳过注册")
        else:
            print(f"❌ 用户注册失败: {response.text}")
            return
    except Exception as e:
        print(f"❌ 注册请求失败: {e}")
        return
    
    # 2. 测试用户登录
    print("\n2. 测试用户登录...")
    login_data = {
        'student_id': '2021001',
        'full_name': '测试用户',
        'password': '123456'
    }
    
    try:
        response = requests.post(f'{API_BASE}/login', json=login_data)
        if response.status_code == 200:
            user_info = response.json()
            print(f"✅ 用户登录成功: {user_info['full_name']}")
        else:
            print(f"❌ 用户登录失败: {response.text}")
            return
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return
    
    # 3. 测试报告校园卡
    print("\n3. 测试报告校园卡...")
    report_data = {
        'card_number': '2021001',
        'found_location': '图书馆',
        'handler_option': 1,
        'contact': '13800138000',
        'current_user_id': user_info['user_id'],
        'current_user_name': user_info['full_name']
    }
    
    try:
        response = requests.post(f'{API_BASE}/report_card', json=report_data)
        if response.status_code == 200:
            print("✅ 校园卡报告成功")
        else:
            print(f"❌ 校园卡报告失败: {response.text}")
            return
    except Exception as e:
        print(f"❌ 报告请求失败: {e}")
        return
    
    # 4. 测试查询校园卡
    print("\n4. 测试查询校园卡...")
    try:
        response = requests.get(f'{API_BASE}/query_lost_card?student_id=2021001')
        if response.status_code == 200:
            query_result = response.json()
            print(f"✅ 查询成功: {query_result['status']}")
            if query_result['status'] == 'found':
                card_id = query_result.get('card_id')
                print(f"   卡片ID: {card_id}")
                
                # 5. 测试删除功能
                print("\n5. 测试删除功能...")
                delete_data = {
                    'card_id': card_id,
                    'current_user_student_id': '2021001'
                }
                
                try:
                    response = requests.post(f'{API_BASE}/delete_card_record', json=delete_data)
                    if response.status_code == 200:
                        delete_result = response.json()
                        if delete_result.get('success'):
                            print("✅ 删除成功")
                        else:
                            print(f"❌ 删除失败: {delete_result.get('error')}")
                    else:
                        print(f"❌ 删除请求失败: {response.text}")
                except Exception as e:
                    print(f"❌ 删除请求异常: {e}")
                
                # 6. 验证删除结果
                print("\n6. 验证删除结果...")
                try:
                    response = requests.get(f'{API_BASE}/query_lost_card?student_id=2021001')
                    if response.status_code == 200:
                        verify_result = response.json()
                        if verify_result['status'] == 'not_found':
                            print("✅ 删除验证成功：记录已不存在")
                        else:
                            print(f"❌ 删除验证失败：记录仍然存在 - {verify_result['status']}")
                    else:
                        print(f"❌ 验证请求失败: {response.text}")
                except Exception as e:
                    print(f"❌ 验证请求异常: {e}")
            else:
                print("ℹ️ 未找到校园卡记录，无法测试删除功能")
        else:
            print(f"❌ 查询失败: {response.text}")
    except Exception as e:
        print(f"❌ 查询请求失败: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == '__main__':
    test_delete_function()
